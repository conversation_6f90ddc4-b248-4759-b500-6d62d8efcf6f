package cmc.pad.resource.admin.api.controller;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * 租赁控制器测试套件
 * 用于批量运行所有租赁相关的控制器测试
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    AdvertisingPointLeasingControllerBasicTest.class,
    FixedPointLeasingControllerBasicTest.class
})
public class LeasingControllerTestSuite {
    // 测试套件类，无需实现任何方法
    // 通过注解配置要运行的测试类
}