package cmc.pad.resource.admin.service.spring;

import cmc.pad.resource.admin.service.iface.AdvertisingPointLeasingService;
import cmc.pad.resource.admin.service.iface.ContractCollectService;
import cmc.pad.resource.admin.service.iface.FixedPointLeasingService;
import mtime.lark.net.rpc.RpcClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * 提供 Spring 自动注入默认服务代理功能。
 */
@Configuration
@Lazy
@Order(Ordered.LOWEST_PRECEDENCE + 10)
public class PadAdminServiceProxyAutoConfig {

    private String SERVER = "cmc.pad.resource.admin.service";

    @Bean
    @ConditionalOnMissingBean
    public ContractCollectService AuthorizedCardManageServiceProxy() {
        return RpcClient.get(SERVER, ContractCollectService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public AdvertisingPointLeasingService AdvertisingPointLeasingServiceProxy() {
        return RpcClient.get(SERVER, AdvertisingPointLeasingService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public FixedPointLeasingService fixedPointLeasingServiceProxy() {
        return RpcClient.get(SERVER, FixedPointLeasingService.class);
    }
}
